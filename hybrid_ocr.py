#!/usr/bin/env python3
"""
Hybrid OCR using Google Cloud Vision for bounding boxes and Gemini for better text recognition.
"""
import os
import sys
import json
import base64
from pathlib import Path
from typing import Dict, List, Any

def setup_credentials():
    """Set up Google Cloud credentials if not already configured."""
    if os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        return True
    
    possible_files = [
        'google-cloud-key.json',
        'service-account-key.json', 
        'credentials.json'
    ]
    
    for file_name in possible_files:
        if os.path.exists(file_name):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_name
            print(f"Using credentials from: {file_name}")
            return True
    
    print("ERROR: No Google Cloud credentials found!")
    return False

def get_vision_data(image_path: str) -> Dict[str, Any]:
    """Get text detection data from Google Cloud Vision API."""
    if not setup_credentials():
        return None
    
    try:
        from google.cloud import vision
        client = vision.ImageAnnotatorClient()
    except Exception as e:
        print(f"Error creating Vision client: {e}")
        return None

    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return None

    with open(image_path, "rb") as image_file:
        content = image_file.read()

    image = vision.Image(content=content)

    try:
        response = client.text_detection(image=image)
        
        if response.error.message:
            raise Exception(f"{response.error.message}")
        
        texts = response.text_annotations
        if not texts:
            return {"full_text": "", "elements": []}
        
        # Extract individual text elements with bounding boxes
        elements = []
        for text in texts[1:]:  # Skip first element (full text)
            vertices = [(vertex.x, vertex.y) for vertex in text.bounding_poly.vertices]
            elements.append({
                "text": text.description,
                "bounding_box": vertices
            })
        
        return {
            "full_text": texts[0].description if texts else "",
            "elements": elements
        }
        
    except Exception as e:
        print(f"Error during Vision API call: {e}")
        return None

def encode_image_base64(image_path: str) -> str:
    """Encode image to base64 for Gemini API."""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def calculate_vision_cost(num_images: int = 1) -> float:
    """Calculate Google Cloud Vision API cost."""
    # Vision API pricing (as of 2024): $1.50 per 1000 units for TEXT_DETECTION
    # First 1000 units per month are free
    cost_per_1000_units = 1.50
    cost_per_unit = cost_per_1000_units / 1000
    return num_images * cost_per_unit

def calculate_gemini_cost(input_tokens: int, output_tokens: int) -> float:
    """Calculate Gemini API cost."""
    # Gemini 1.5 Flash pricing (as of 2024):
    # Input: $0.075 per 1M tokens
    # Output: $0.30 per 1M tokens
    input_cost_per_1m = 0.075
    output_cost_per_1m = 0.30

    input_cost = (input_tokens / 1_000_000) * input_cost_per_1m
    output_cost = (output_tokens / 1_000_000) * output_cost_per_1m

    return input_cost + output_cost

def estimate_tokens(text: str, image_size_mb: float = 1.0) -> int:
    """Estimate token count for Gemini API."""
    # Rough estimation: 1 token ≈ 4 characters for text
    # Images: approximately 258 tokens per MB
    text_tokens = len(text) // 4
    image_tokens = int(image_size_mb * 258)
    return text_tokens + image_tokens

def call_gemini_api(image_path: str, vision_data: Dict[str, Any]) -> Dict[str, Any]:
    """Call Gemini API to get corrected text recognition and translation."""

    # Check for Gemini API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("ERROR: GEMINI_API_KEY environment variable not set!")
        print("Please set your Gemini API key: export GEMINI_API_KEY=your_api_key_here")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        return None

    try:
        import requests
    except ImportError:
        print("Installing requests library...")
        os.system("pip install requests")
        import requests

    # Encode image
    image_base64 = encode_image_base64(image_path)

    # Get image size for cost estimation
    image_size_mb = os.path.getsize(image_path) / (1024 * 1024)

    # Prepare the prompt
    vision_elements = vision_data.get("elements", [])
    vision_text_list = [elem["text"] for elem in vision_elements]

    prompt = f"""
You are an expert OCR correction and translation system. I have an image with text that was processed by Google Cloud Vision API. The Vision API detected individual text elements, but some may have errors or be fragments that should be combined.

Here are the individual text elements that Google Cloud Vision detected:
{json.dumps(vision_text_list, indent=2)}

IMPORTANT RULES:
1. Each element in the list corresponds to a separate bounding box in the image
2. If an element is just a "%" symbol, it likely belongs with the preceding number - DO NOT change it to a completely different word
3. If multiple elements should be combined (like "5" and "%"), keep them as separate elements but make sure they're consistent
4. Only correct actual OCR errors, don't completely replace text with different words
5. If text is already in English or is a brand name, keep the translation the same as the corrected text

Please analyze the image and provide:
1. Corrected text recognition for each element (fix OCR errors only)
2. English translation for each element
3. Language detection for the original text

Return your response as a JSON object with this exact structure:
{{
  "corrected_elements": [
    {{
      "original_text": "text_from_vision_api",
      "corrected_text": "your_corrected_version_of_same_text",
      "english_translation": "english_translation_of_corrected_text",
      "detected_language": "language_code_or_name",
      "confidence": "high|medium|low"
    }}
  ],
  "full_corrected_text": "complete corrected text from the image",
  "full_english_translation": "complete english translation of the corrected text",
  "detected_language": "primary_language_detected",
  "notes": "any observations about corrections and translations made"
}}

Focus on:
1. Correcting OCR errors (misread characters, wrong words) but keeping the same meaning
2. Fixing spacing and formatting issues
3. Handling special characters and diacritics correctly
4. Providing accurate English translations
5. Maintaining the original structure and meaning
6. NOT replacing symbols like "%" with completely different words
7. Keeping brand names and English words unchanged in translation
"""

    # Prepare API request
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "contents": [{
            "parts": [
                {"text": prompt},
                {
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": image_base64
                    }
                }
            ]
        }],
        "generationConfig": {
            "response_mime_type": "application/json"
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()

        result = response.json()

        if 'candidates' in result and len(result['candidates']) > 0:
            content = result['candidates'][0]['content']['parts'][0]['text']

            # Calculate cost
            input_tokens = estimate_tokens(prompt, image_size_mb)
            output_tokens = estimate_tokens(content, 0)  # No image in output
            gemini_cost = calculate_gemini_cost(input_tokens, output_tokens)

            parsed_result = json.loads(content)
            parsed_result['cost_info'] = {
                'input_tokens': input_tokens,
                'output_tokens': output_tokens,
                'cost_usd': gemini_cost
            }

            return parsed_result
        else:
            print("No response from Gemini API")
            return None

    except requests.exceptions.RequestException as e:
        print(f"Error calling Gemini API: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing Gemini response: {e}")
        print(f"Raw response: {content}")
        return None

def combine_results(vision_data: Dict[str, Any], gemini_data: Dict[str, Any]) -> Dict[str, Any]:
    """Combine Vision API bounding boxes with Gemini's corrected text and translations."""
    if not vision_data or not gemini_data:
        return None

    combined_elements = []
    vision_elements = vision_data.get("elements", [])
    corrected_elements = gemini_data.get("corrected_elements", [])

    # Create a mapping of original to corrected text
    correction_map = {}
    for correction in corrected_elements:
        correction_map[correction["original_text"]] = correction

    # Combine bounding boxes with corrected text and translations
    for vision_elem in vision_elements:
        original_text = vision_elem["text"]
        correction = correction_map.get(original_text, {})

        combined_elements.append({
            "original_text": original_text,
            "corrected_text": correction.get("corrected_text", original_text),
            "english_translation": correction.get("english_translation", original_text),
            "detected_language": correction.get("detected_language", "unknown"),
            "confidence": correction.get("confidence", "unknown"),
            "bounding_box": vision_elem["bounding_box"]
        })

    # Calculate total costs
    vision_cost = calculate_vision_cost(1)  # 1 image processed
    gemini_cost = gemini_data.get("cost_info", {}).get("cost_usd", 0)
    total_cost = vision_cost + gemini_cost

    return {
        "elements": combined_elements,
        "full_corrected_text": gemini_data.get("full_corrected_text", ""),
        "full_english_translation": gemini_data.get("full_english_translation", ""),
        "detected_language": gemini_data.get("detected_language", "unknown"),
        "gemini_notes": gemini_data.get("notes", ""),
        "total_elements": len(combined_elements),
        "cost_breakdown": {
            "google_cloud_vision_usd": vision_cost,
            "gemini_api_usd": gemini_cost,
            "total_usd": total_cost,
            "gemini_tokens": gemini_data.get("cost_info", {}),
            "note": "Costs are estimates based on current pricing. First 1000 Vision API calls per month are free."
        }
    }

def hybrid_ocr(image_path: str) -> Dict[str, Any]:
    """Perform hybrid OCR using both Vision API and Gemini."""
    print(f"Processing image: {image_path}")
    print("=" * 60)
    
    # Step 1: Get Vision API data
    print("Step 1: Getting bounding boxes from Google Cloud Vision...")
    vision_data = get_vision_data(image_path)
    if not vision_data:
        return None
    
    print(f"Vision API detected {len(vision_data['elements'])} text elements")
    
    # Step 2: Get Gemini corrections
    print("Step 2: Getting text corrections from Gemini...")
    gemini_data = call_gemini_api(image_path, vision_data)
    if not gemini_data:
        print("Failed to get Gemini corrections, using Vision API results only")
        return vision_data
    
    # Step 3: Combine results
    print("Step 3: Combining results...")
    combined_data = combine_results(vision_data, gemini_data)
    
    return combined_data

if __name__ == "__main__":
    image_path = 'jar.jpg'
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    
    result = hybrid_ocr(image_path)
    
    if result:
        print("\n" + "=" * 60)
        print("HYBRID OCR RESULTS")
        print("=" * 60)
        
        # Save results to JSON file
        output_file = f"{Path(image_path).stem}_ocr_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\nFull results saved to: {output_file}")
        
        # Display summary
        if "full_corrected_text" in result:
            print(f"\nCorrected Text:\n{result['full_corrected_text']}")

        if "full_english_translation" in result:
            print(f"\nEnglish Translation:\n{result['full_english_translation']}")

        if "detected_language" in result:
            print(f"\nDetected Language: {result['detected_language']}")

        if "gemini_notes" in result:
            print(f"\nGemini Notes: {result['gemini_notes']}")

        # Display cost information
        if "cost_breakdown" in result:
            cost_info = result['cost_breakdown']
            print(f"\n💰 Cost Breakdown:")
            print(f"   Google Cloud Vision: ${cost_info['google_cloud_vision_usd']:.6f}")
            print(f"   Gemini API: ${cost_info['gemini_api_usd']:.6f}")
            print(f"   Total: ${cost_info['total_usd']:.6f}")

            if 'gemini_tokens' in cost_info:
                tokens = cost_info['gemini_tokens']
                print(f"   Gemini tokens - Input: {tokens.get('input_tokens', 0)}, Output: {tokens.get('output_tokens', 0)}")

        print(f"\nTotal elements processed: {result.get('total_elements', 0)}")

        # Show first few corrections and translations as examples
        elements = result.get('elements', [])
        if elements:
            print("\nExample corrections and translations:")
            for i, elem in enumerate(elements[:5]):
                original = elem['original_text']
                corrected = elem['corrected_text']
                translation = elem['english_translation']

                if original != corrected or corrected != translation:
                    print(f"  Original: '{original}'")
                    if original != corrected:
                        print(f"  Corrected: '{corrected}' ({elem['confidence']})")
                    if corrected != translation:
                        print(f"  Translation: '{translation}'")
                    print(f"  Language: {elem['detected_language']}")
                    print()
    else:
        print("Failed to process image")
