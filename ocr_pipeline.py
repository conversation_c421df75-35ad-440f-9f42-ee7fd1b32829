#!/usr/bin/env python3
"""
Complete OCR Pipeline: Combines hybrid OCR processing with visualization.
This script orchestrates the entire workflow from image processing to visual results.
"""
import os
import sys
import json
import subprocess
import argparse
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"❌ Error: Command not found. Make sure the script exists.")
        return False

def check_file_exists(file_path, description):
    """Check if a file exists and print status."""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description} not found: {file_path}")
        return False

def infer_json_path(image_path):
    """Infer the JSON output path from the image path."""
    image_stem = Path(image_path).stem
    return f"{image_stem}_ocr_results.json"

def get_processed_image_path(json_path, original_image_path):
    """Get the actual processed image path from OCR results."""
    try:
        if os.path.exists(json_path):
            with open(json_path, 'r') as f:
                data = json.load(f)
                img_info = data.get('image_processing', {})
                if img_info.get('rotation_applied', False):
                    processed_path = img_info.get('processed_image', original_image_path)
                    if os.path.exists(processed_path):
                        return processed_path
    except Exception as e:
        print(f"Warning: Could not read processed image info from JSON: {e}")

    return original_image_path

def main():
    parser = argparse.ArgumentParser(
        description="Complete OCR Pipeline: Process image with hybrid OCR and create visualizations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python ocr_pipeline.py image.jpg                    # Full pipeline
  python ocr_pipeline.py image.jpg --no-viz          # OCR only
  python ocr_pipeline.py image.jpg --viz-only        # Visualization only (requires existing JSON)
  python ocr_pipeline.py image.jpg --no-translations # Skip translation visualization
        """
    )
    
    parser.add_argument('image_path', help='Path to the image file to process')
    parser.add_argument('--no-ocr', action='store_true', 
                       help='Skip OCR processing (use existing JSON file)')
    parser.add_argument('--no-viz', action='store_true', 
                       help='Skip visualization (OCR processing only)')
    parser.add_argument('--viz-only', action='store_true', 
                       help='Only create visualization (requires existing JSON file)')
    parser.add_argument('--no-translations', action='store_true', 
                       help='Skip translation visualization panel')
    parser.add_argument('--json-file', 
                       help='Specify custom JSON file path (default: inferred from image name)')
    
    args = parser.parse_args()
    
    # Validate input
    image_path = args.image_path
    if not check_file_exists(image_path, "Input image"):
        return 1
    
    # Determine JSON file path
    json_path = args.json_file if args.json_file else infer_json_path(image_path)
    
    print(f"\n🚀 OCR Pipeline Starting")
    print(f"📷 Image: {image_path}")
    print(f"📄 JSON: {json_path}")
    
    # Step 1: OCR Processing
    if args.viz_only:
        print(f"\n⏭️  Skipping OCR processing (--viz-only specified)")
        if not check_file_exists(json_path, "Required JSON file"):
            print("❌ Cannot proceed with visualization without JSON file")
            return 1
    elif not args.no_ocr:
        success = run_command(
            ['python', 'hybrid_ocr.py', image_path],
            "Running Hybrid OCR Processing"
        )
        
        if not success:
            print("❌ OCR processing failed")
            return 1
        
        # Verify JSON output was created
        if not check_file_exists(json_path, "OCR results JSON"):
            print("❌ OCR processing completed but JSON file not found")
            return 1
    else:
        print(f"\n⏭️  Skipping OCR processing (--no-ocr specified)")
        if not check_file_exists(json_path, "Required JSON file"):
            print("❌ Cannot proceed without JSON file")
            return 1
    
    # Step 2: Visualization
    if args.no_viz:
        print(f"\n⏭️  Skipping visualization (--no-viz specified)")
    else:
        # Use the processed image path if rotation was applied
        viz_image_path = get_processed_image_path(json_path, image_path)
        if viz_image_path != image_path:
            print(f"📷 Using rotated image for visualization: {viz_image_path}")

        viz_command = ['python', 'visualize_ocr.py', json_path, viz_image_path]
        if args.no_translations:
            viz_command.append('--no-translations')

        success = run_command(
            viz_command,
            "Creating OCR Visualization"
        )
        
        if not success:
            print("❌ Visualization failed")
            return 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"✅ OCR Pipeline Completed Successfully!")
    print(f"{'='*60}")
    
    # List output files
    print(f"\n📁 Output Files:")
    
    if os.path.exists(json_path):
        file_size = os.path.getsize(json_path)
        print(f"   📄 OCR Results: {json_path} ({file_size:,} bytes)")
    
    # Look for visualization files
    image_stem = Path(image_path).stem
    viz_file = f"{image_stem}_ocr_visualization.png"
    if os.path.exists(viz_file):
        file_size = os.path.getsize(viz_file)
        print(f"   🎨 Visualization: {viz_file} ({file_size:,} bytes)")
    
    # Look for overlay files (if they exist from other scripts)
    for overlay_type in ['seamless', 'highlight']:
        overlay_file = f"{image_stem}_{overlay_type}.jpg"
        if os.path.exists(overlay_file):
            file_size = os.path.getsize(overlay_file)
            print(f"   🖼️  {overlay_type.title()} Overlay: {overlay_file} ({file_size:,} bytes)")

    # Look for rotated image
    rotated_file = f"{image_stem}_rotated{Path(image_path).suffix}"
    if os.path.exists(rotated_file):
        file_size = os.path.getsize(rotated_file)
        print(f"   🔄 Rotated Image: {rotated_file} ({file_size:,} bytes)")

    print(f"\n💡 Next Steps:")
    print(f"   • View the visualization: {viz_file}")
    print(f"   • Check the detailed JSON results: {json_path}")

    # Use the processed image for overlay creation
    overlay_image_path = get_processed_image_path(json_path, image_path)
    print(f"   • Create translation overlays: python translate_overlay.py {json_path} {overlay_image_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
